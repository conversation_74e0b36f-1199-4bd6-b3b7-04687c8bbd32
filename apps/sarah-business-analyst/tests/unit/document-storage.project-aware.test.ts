import { DocumentStorage } from '../../src/services/storage/implementations/document-storage.js';
import { ProjectMemory, ProjectContext } from '../../src/project-memory.js';

describe('DocumentStorage (project-aware, discussion-friendly)', () => {
  let projectMemory: ProjectMemory;
  let mockStorage: any;
  let documentStorage: DocumentStorage;

  beforeEach(() => {
    projectMemory = new ProjectMemory();
    mockStorage = { save: jest.fn(async () => ({ success: true, url: 'mock-url' })) };
    documentStorage = new DocumentStorage(
      mockStorage,
      () => projectMemory.getCurrentProject(),
      () => projectMemory.getRecentProjects()
    );
  });

  it('returns friendly message if no project is set and no history', async () => {
    const result = await documentStorage.save('file.md', 'content');
    expect(result.success).toBe(false);
    expect(result.error).toMatch(/haven’t selected a project/i);
    expect(result.error).toMatch(/choose a project/i);
  });

  it('returns suggestion to continue last project if history exists', async () => {
    const lastProject: ProjectContext = {
        name: 'Test Project',
        githubRepo: 'repo',
        organization: 'org',
        docsPath: '/docs',
        githubOwner: 'owner',
        githubBranch: 'main'
    };
    projectMemory.setProject(lastProject);
    // Clear current project
    (projectMemory as any).currentProject = null;
    const result = await documentStorage.save('file.md', 'content');
    expect(result.success).toBe(false);
    expect(result.error).toMatch(/continue with your last project/i);
    expect(result.error).toMatch(/Test Project/);
  });

  it('saves successfully when project context is set with github info', async () => {
    const project: ProjectContext = {
        name: 'Active',
        githubRepo: 'repo',
        organization: 'org',
        docsPath: '/docs',
        githubOwner: 'owner',
        githubBranch: 'main'
    };
    projectMemory.setProject(project);
    const result = await documentStorage.save('file.md', 'content');
    expect(result.success).toBe(true);
    expect(result.url).toBe('mock-url');
    expect(mockStorage.save).toHaveBeenCalled();
  });
});
