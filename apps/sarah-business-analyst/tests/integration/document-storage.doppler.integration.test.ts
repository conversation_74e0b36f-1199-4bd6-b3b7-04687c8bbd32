import { StorageConfig } from '../../src/config/storage-config.js';
import { ProjectMemory, ProjectContext } from '../../src/project-memory.js';
import { DocumentStorage } from '../../src/services/storage/implementations/document-storage.js';
import dotenv from 'dotenv';

dotenv.config();

describe('Integration: Document Storage with Doppler', () => {
  let projectMemory: ProjectMemory;
  let documentStorage: DocumentStorage;

  const testProject: ProjectContext = {
    name: 'itsatest',
    githubRepo: 'tets-project',
    organization: 'Infinisoft-inc',
    docsPath: 'projects/itsatest/docs',
    githubOwner: 'Infinisoft-inc',
    githubBranch: 'main',
  };

  beforeAll(async () => {
    projectMemory = new ProjectMemory();
    projectMemory.setProject(testProject);
    const { storage } = await StorageConfig.createWithDopplerIntegration(
      () => projectMemory.getCurrentProject(),
      () => projectMemory.getRecentProjects()
    );
    documentStorage = new DocumentStorage(
      storage,
      () => projectMemory.getCurrentProject(),
      () => projectMemory.getRecentProjects()
    );
  });

  it('should save a document to GitHub using Doppler credentials', async () => {
    const result = await documentStorage.save('projects/itsatest/docs/business-case.test.md', '# Test Business Case\nThis is a test.');
    expect(result.success).toBe(true);
    expect(result.url).toMatch(/github.com/);
  });
});
