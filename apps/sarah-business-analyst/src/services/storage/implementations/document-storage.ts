/**
 * Document Saver - Single Responsibility
 * Only handles saving documents to storage
 */

import { ProjectContext } from 'apps/sarah-business-analyst/src/project-memory.js';
import { StorageService } from '../storage.js';

export interface SaveResult {
  success: boolean;
  url?: string;
  error?: string;
}

export class DocumentStorage {
  constructor(
    private storage: StorageService,
    private getProjectContext: () => ProjectContext | null,
    private getRecentProjects: () => ProjectContext[]
  ) {}

  async save(path: string, content: string): Promise<SaveResult> {
    const project = this.getProjectContext();
    console.log('[DocumentStorage] getProjectContext returned:', project);
    if (!project || !project.githubOwner || !project.githubRepo) {
      const recent = this.getRecentProjects();
      console.log('[DocumentStorage] No valid project context. Recent projects:', recent);
      if (recent.length > 0) {
        return {
          success: false,
          error: `Hi! You haven’t selected a project yet. Would you like to continue with your last project, “${recent[0].name}”?`
        };
      }
      return {
        success: false,
        error: "Hi! You haven’t selected a project yet. Please choose a project before saving your work."
      };
    }
    try {
      const result = await this.storage.save(path, content);
      console.log('[DocumentStorage] Storage save result:', result);
      return result;
    } catch (error) {
      console.error('[DocumentStorage] Error during save:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}
