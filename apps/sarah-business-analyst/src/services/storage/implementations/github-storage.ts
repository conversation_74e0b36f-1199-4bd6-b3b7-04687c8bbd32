import { StorageService, DocumentInfo } from "../storage.js";
import type { Octokit } from "@octokit/rest";
import { ProjectMemory } from "../../../project-memory.js";
import type { ProjectContext } from '../../../project-memory';

/**
 * GitHub storage implementation - Project-context aware
 */
export class GitHubStorage implements StorageService {
  private octokit: any; // Accept any Octokit-like instance for compatibility
  private getProjectContext: () => ProjectContext | null;
  private getRecentProjects: () => ProjectContext[];

  constructor(
    octokit: any, // Accept any Octokit-like instance for compatibility
    getProjectContext: () => ProjectContext | null,
    getRecentProjects: () => ProjectContext[]
  ) {
    this.octokit = octokit;
    this.getProjectContext = getProjectContext;
    this.getRecentProjects = getRecentProjects;
  }

  async save(path: string, content: string) {
    const project = this.getProjectContext();
    if (!project || !project.githubOwner || !project.githubRepo) {
      const recent = this.getRecentProjects();
      if (recent.length > 0) {
        return {
          success: false,
          message: `Hi! You haven’t selected a project yet. Would you like to continue with your last project, “${recent[0].name}”?`
        };
      }
      return {
        success: false,
        message: "Hi! You haven’t selected a project yet. Please choose a project before saving your work."
      };
    }

    try {
      // Check if file exists to get SHA for updates
      let sha: string | undefined;
      try {
        const { data } = await this.octokit.rest.repos.getContent({
          owner: project.githubOwner,
          repo: project.githubRepo,
          path,
          ref: project.githubBranch,
        });

        if ('sha' in data) {
          sha = data.sha;
        }
      } catch (error) {
        // File doesn't exist, that's fine for new files
      }

      // Create or update file
      const { data } = await this.octokit.rest.repos.createOrUpdateFileContents({
        owner: project.githubOwner,
        repo: project.githubRepo,
        path,
        message: `Update ${path}`,
        content: Buffer.from(content).toString('base64'),
        committer: {
          name: 'Sarah - AI Business Analyst',
          email: '<EMAIL>'
        },
        author: {
          name: 'Sarah - AI Business Analyst',
          email: '<EMAIL>'
        },
        branch: project.githubBranch,
        ...(sha && { sha }),
      });

      return {
        success: true,
        url: data.content?.html_url || `https://github.com/${project.githubOwner}/${project.githubRepo}/blob/${project.githubBranch}/${path}`,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  async read(path: string): Promise<{ success: boolean; content?: string; error?: string; }> {
    const project = this.getProjectContext();
    if (!project || !project.githubOwner || !project.githubRepo) {
      return {
        success: false,
        error: "Project context is not set. Please select a project to continue.",
      };
    }

    try {
      const { data } = await this.octokit.rest.repos.getContent({
        owner: project.githubOwner,
        repo: project.githubRepo,
        path,
        ref: project.githubBranch,
      });

      if ('content' in data && data.content) {
        const content = Buffer.from(data.content, 'base64').toString('utf-8');
        return {
          success: true,
          content,
        };
      }

      return {
        success: false,
        error: 'File content not available',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  async list(): Promise<{ success: boolean; documents?: DocumentInfo[]; error?: string; }> {
    const project = this.getProjectContext();
    if (!project || !project.githubOwner || !project.githubRepo) {
      return {
        success: false,
        error: "Project context is not set. Please select a project to continue.",
      };
    }

    try {
      // For simplicity, we'll list files in the root directory
      // In a real implementation, you might want to recursively list all files
      const { data } = await this.octokit.rest.repos.getContent({
        owner: project.githubOwner,
        repo: project.githubRepo,
        path: '',
        ref: project.githubBranch,
      });

      if (Array.isArray(data)) {
        const documents: DocumentInfo[] = data
          .filter(item => item.type === 'file')
          .map(item => ({
            path: item.path,
            name: item.name,
            size: item.size,
            url: item.html_url || undefined,
          }));

        return {
          success: true,
          documents,
        };
      }

      return {
        success: true,
        documents: [],
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }
}
