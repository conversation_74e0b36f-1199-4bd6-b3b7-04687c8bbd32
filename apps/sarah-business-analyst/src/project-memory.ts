/**
 * Simple Project Memory - Single Responsibility
 * Only handles project information storage
 */

export interface ProjectInfo {
  name?: string;
  information: string[];
}

export interface ProjectContext {
  name: string;
  githubRepo: string;
  organization: string;
  docsPath: string;
  githubOwner: string;
  githubBranch: string;
}

/**
 * Clean project memory implementation
 */
export class ProjectMemory {
  private info: ProjectInfo = { information: [] };
  private currentProject: ProjectContext | null = null;
  private projectHistory: ProjectContext[] = [];

  /**
   * Add information to project memory
   */
  add(information: string): void {
    if (!this.info.information.includes(information)) {
      this.info.information.push(information);
    }
  }

  /**
   * Set project name
   */
  setName(name: string): void {
    this.info.name = name;
  }

  /**
   * Get all project information
   */
  getAll(): string[] {
    return [...this.info.information];
  }

  /**
   * Get project name
   */
  getName(): string | undefined {
    return this.info.name;
  }

  /**
   * Check if has information
   */
  hasInfo(): boolean {
    return this.info.information.length > 0;
  }

  /**
   * Clear all information
   */
  clear(): void {
    this.info = { information: [] };
  }

  /**
   * Get summary for display
   */
  getSummary(): string {
    const name = this.info.name || 'Unnamed Project';
    const count = this.info.information.length;
    return `Project: ${name} (${count} pieces of information)`;
  }

  setProject(context: ProjectContext) {
    this.currentProject = context;
    // Add to history, avoid duplicates by name
    this.projectHistory = [context, ...this.projectHistory.filter(p => p.name !== context.name)];
  }

  getCurrentProject(): ProjectContext | null {
    return this.currentProject;
  }

  getRecentProjects(): ProjectContext[] {
    return this.projectHistory.slice(0, 3);
  }
}
