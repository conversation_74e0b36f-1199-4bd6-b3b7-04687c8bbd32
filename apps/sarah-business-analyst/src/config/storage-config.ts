/**
 * Storage Configuration with Strongly Typed Options
 * Uses discriminative unions for type safety and integration service
 */

import { StorageService } from '../services/storage/index.js';
import { getGitHubCredentials, createGitHubClient } from '@brainstack/integration-service';
import { GitHubStorage } from '../services/storage/implementations/github-storage.js';
import type { ProjectContext } from '../project-memory.js';


/**
 * Strongly typed storage options using discriminative unions
 */
export type StorageOptions =
  | { type: 'mock' }
  | { type: 'file'; basePath: string }
  | {
    type: 'github';
    token: string;
    owner: string;
    repo: string;
    branch?: string
  };

/**
 * Doppler configuration structure (single secret pattern)
 */
export interface DopplerConfig {
  storage?: {
    type: 'mock' | 'file' | 'github';
    basePath?: string;
  };
  github?: {
    token: string;
    owner?: string;
    repo: string;
    branch?: string;
  };
}

export class StorageConfig {
  /**
   * Create storage with Doppler integration service ONLY (single secret pattern)
   */
  static async createWithDopplerIntegration(getProjectContext: () => ProjectContext | null, getRecentProjects: () => ProjectContext[]): Promise<{ storage: StorageService; message: string; success: boolean }> {
    const dopplerToken = process.env.DOPPLER_TOKEN;
    if (!dopplerToken) {
      throw new Error('Hi! I need your Doppler token to connect to GitHub. Please set the DOPPLER_TOKEN environment variable.');
    }
    try {
      // Use integration service to get GitHub credentials and client
      const githubCredsResult = await getGitHubCredentials({ token: dopplerToken });
      if (githubCredsResult.success && githubCredsResult.data) {
        const octokit = createGitHubClient(githubCredsResult.data);
        return {
          storage: new GitHubStorage(octokit, getProjectContext, getRecentProjects),
          message: '✅ GitHub client created using Doppler integration service. Ready to work with your project repositories!',
          success: true
        };
      } else {
        throw new Error(`Sorry, I couldn’t create a GitHub client from Doppler: ${githubCredsResult.error}`);
      }
    } catch (error) {
      throw new Error(`Sorry, there was a problem with Doppler integration: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
